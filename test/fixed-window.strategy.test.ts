import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { Limiter } from '../app/utils/limiter/limiter'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

process.chdir(path.join(__dirname, '..'))

function assert(condition: any, message: string): void {
    if (!condition) {
        throw new Error(`Assertion failed: ${message}`)
    }
}

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

async function runTest() {
    console.log('--- Running FixedWindowStrategy Test ---')

    const CAPACITY = 5
    const WINDOW = 1000 // 1 second
    const limiter = new Limiter({ maxRequestsPerSecond: CAPACITY })

    let limitEventCount = 0
    let releaseEventCount = 0
    let lastUntil: Date | null = null
    let limitEventFiredInWindow = false
    let releaseEventFiredAfterLimit = false

    limiter.on('limit', (until) => {
        console.log(`[EVENT] 'limit' fired. Will reset at: ${until.toISOString()}`)
        limitEventCount++
        lastUntil = until
        limitEventFiredInWindow = true
    })

    limiter.on('release', () => {
        console.log(`[EVENT] 'release' fired.`)
        releaseEventCount++

        if (limitEventFiredInWindow) {
            releaseEventFiredAfterLimit = true
        }
    })

    // --- Test Case 1: Trigger 'limit' event ---
    console.log(`\n[Test 1] Sending ${CAPACITY + 2} requests to trigger limit...`)

    const requests: Array<Promise<any>> = []

    for (let i = 0; i < CAPACITY + 2; i++) {
        const p = limiter.schedule(async () => {
            console.log(`Request #${i + 1} processed.`)
            await delay(50) // Simulate some work

            return i + 1
        }).catch((error) => {
            console.error(`Request #${i + 1} failed as expected: ${error.message}`)
        })

        requests.push(p)
    }

    await Promise.allSettled(requests)

    console.log('\n--- Analyzing Test 1 ---')

    try {
        assert(limitEventCount === 1, `'limit' event should fire exactly once. Fired: ${limitEventCount}`)
        console.log('✔ PASSED: \'limit\' event fired once.')

        assert(lastUntil !== null, '`until` parameter should not be null.')
        const expectedUntil = Date.now() + WINDOW
        const untilTime = lastUntil!.getTime()
        assert(Math.abs(expectedUntil - untilTime) < 500, `\`until\` time is incorrect. Expected around ${new Date(expectedUntil).toISOString()}, got ${lastUntil!.toISOString()}`)
        console.log('✔ PASSED: `until` parameter is a valid future date.')

        assert(releaseEventCount === 0, `'release' event should not fire yet. Fired: ${releaseEventCount}`)
        console.log('✔ PASSED: \'release\' event has not fired.')

        console.log('--- Test 1 Passed ---\n')
    } catch (error: any) {
        console.error(`--- Test 1 FAILED: ${error.message} ---`)
        process.exit(1)
    }

    // --- Test Case 2: Trigger 'release' event after window reset ---
    console.log(`[Test 2] Waiting for window to reset (${WINDOW + 100}ms)...`)
    await delay(WINDOW + 100)

    console.log('Sending 1 more request to trigger release...')

    // Reset trackers for the new window
    limitEventFiredInWindow = false
    releaseEventFiredAfterLimit = false

    await limiter.schedule(async () => {
        console.log('Request after window reset processed.')
    })

    console.log('\n--- Analyzing Test 2 ---')

    try {
        assert(limitEventCount === 1, `'limit' event count should remain 1. Count: ${limitEventCount}`)
        console.log('✔ PASSED: \'limit\' event was not fired again.')

        assert(releaseEventCount === 1, `'release' event should have fired once. Fired: ${releaseEventCount}`)
        console.log('✔ PASSED: \'release\' event fired.')

        assert(releaseEventFiredAfterLimit, `'release' should fire after a 'limit' cycle.`)
        console.log('✔ PASSED: \'release\' event was correctly correlated with a previous \'limit\'.')
        console.log('--- Test 2 Passed ---\n')
    } catch (error: any) {
        console.error(`--- Test 2 FAILED: ${error.message} ---`)
        process.exit(1)
    }

    console.log('--- All tests passed successfully! ---')
}

runTest().catch((error) => {
    console.error('An unexpected error occurred during the test run:', error)
    process.exit(1)
})
