import type { Emitter } from '@kdt310722/utils/event'
import type { AnyObject } from '@kdt310722/utils/object'
import type { Awaitable } from '@kdt310722/utils/promise'

export interface LimiterScheduleOptions {
    weight?: number
    priority?: number
}

export type StrategyEvents<TMetadata = AnyObject> = {
    limit: (until: Date, metadata: TMetadata) => void
    release: () => void
    error: (error: unknown) => void
}

export abstract class Strategy<TConfig extends AnyObject = AnyObject, TMetadata extends AnyObject = AnyObject> {
    public constructor(protected readonly config: TConfig, protected readonly emitter: Emitter<StrategyEvents<TMetadata>>) {}

    public abstract schedule<T>(fn: () => Awaitable<T>, options?: LimiterScheduleOptions): Promise<T>
}
